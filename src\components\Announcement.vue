<template>
    <!-- 停水公告模块 -->
    <div class="announcement common">
        <!-- 标题区域：包含标题文本和图标 -->
        <div class="announcement-bg common-bg text-left">
            <div class="announcement-title common-title padding-left-xll">停水公告</div>
            <img src="../../src/assets/img/announcement-service.png" alt="公告服务图标">
        </div>

        <!-- 公告列表滚动区域 -->
        <div class="overflow-hidden" ref="scrollContainer">
            <!-- 滚动内容容器（用于实现无缝滚动） -->
            <div v-if="displayData.length" class="scroll-wrapper" ref="scrollWrapper">
                <!-- 循环渲染公告项 -->
                <div v-for="(item, index) in displayData" :key="item.id || index"
                    class="margin-top-sm font-size-df flex align-center"
                    :class="['an-con', hoverClass(index), { 'active': activeIndex === index }]"
                    @mouseenter="handleHover(index)" @mouseleave="handleMouseLeave(index)">
                    <!-- 公告类型（如“紧急停水”） -->
                    <div :class="hoverTitle(index)" class="an-title">{{ item.stopTypeName }}</div>

                    <!-- 公告详情区域 -->
                    <div class="an-right flex space-between align-center">
                        <div class="text-left">
                            <div :class="colorClass(index)" class="an-subTitle font-size-lg overflow-ellipsis">{{
                                item.stopReason }}</div>
                            <div class="color-6c overflow-ellipsis">{{ item.stopAddress }}</div>
                            <div class="color-6c">{{ item.stopStartTime }}至 {{ item.stopEndTime }}</div>
                        </div>

                        <!-- 查看详情按钮 -->
                        <div class="font-size-sm" :class="hoverTitle(index)">
                            <div :class="['common-btn', 'check-more', hoverBtnClass(index), clickBtnClass(index)]"
                                @mouseenter="handleBtnHover(index)" @mouseleave="handleMouseBtnLeave(index)"
                                @click="handleBtnClick(index)">
                                查看详情
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详情弹窗（点击“查看详情”时显示） -->
        <div v-if="isDetailVisible" class="dialog">
            <div class="dialog-bg">
                <!-- 弹窗标题栏 -->
                <div class="d-t flex space-between align-center">
                    <img class="d-t-b" src="../assets/img/an-service.png" alt="">
                    <div class="dialog-title common-title">紧急停水</div>
                    <!-- 关闭按钮（带状态切换效果） -->
                    <div class="d-t-c pointer" @click="closeD" @mouseenter="handleMouseEnter"
                        @mouseleave="handleMouseLeave1" @mousedown="handleMouseDown" @mouseup="handleMouseUp">
                        <img :src="currentImage" class="">
                        <!-- 预加载关闭按钮的三种状态图片（优化切换性能） -->
                        <img v-if="false" :src="normalImage" alt="预加载">
                        <img v-if="false" :src="hoverImage" alt="预加载">
                        <img v-if="false" :src="clickImage" alt="预加载">
                    </div>
                </div>

                <!-- 弹窗内容区域 -->
                <div class="dialog-content margin-tb">
                    <el-row class="row-bg" :gutter="20">
                        <!-- 停水类型 -->
                        <el-col :span="12">
                            <div
                                :class="['flex', 'text-left font-size-df', isMultiLine(displayData[activeIndex]?.stopTypeName) ? 'align-start' : 'align-center']">
                                <div class="color-blue flex space-between align-center">
                                    <div class="common-dian margin-right-sm"></div>
                                    <div class="d-c-title"> 停水类型:</div>
                                </div>
                                <div class="margin-left-sm text-justify">
                                    {{ displayData[activeIndex]?.stopTypeName }}
                                </div>
                            </div>
                        </el-col>

                        <!-- 发布单位 -->
                        <el-col :span="12">
                            <div
                                :class="['flex', 'text-left font-size-df', isMultiLine(displayData[activeIndex]?.publishUnit) ? 'align-start' : 'align-center']">
                                <div class="color-blue flex space-between align-center">
                                    <div class="common-dian margin-right-sm"></div>
                                    <div class="d-c-title">发布单位:</div>
                                </div>
                                <div class="margin-left-sm text-justify">
                                    {{ displayData[activeIndex]?.publishUnit }}
                                </div>
                            </div>
                        </el-col>

                        <!-- 停水地址 -->
                        <el-col :span="12">
                            <div
                                :class="['flex', 'text-left font-size-df', isMultiLine(displayData[activeIndex]?.stopAddress) ? 'align-start' : 'align-center']">
                                <div class="color-blue flex space-between align-center">
                                    <div class="common-dian margin-right-sm"></div>
                                    <div class="d-c-title">停水地址:</div>
                                </div>
                                <div class="margin-left-sm text-justify">
                                    {{ displayData[activeIndex]?.stopAddress }}
                                </div>
                            </div>
                        </el-col>

                        <!-- 阀门信息 -->
                        <el-col :span="12">
                            <div
                                :class="['flex', 'text-left font-size-df', isMultiLine(displayData[activeIndex]?.valveInfo) ? 'align-start' : 'align-center']">
                                <div class="color-blue flex space-between align-center">
                                    <div class="common-dian margin-right-sm"></div>
                                    <div class="d-c-title">阀门信息:</div>
                                </div>
                                <div class="margin-left-sm text-justify">
                                    {{ displayData[activeIndex]?.valveInfo }}
                                </div>
                            </div>
                        </el-col>

                        <!-- 停水开始时间 -->
                        <el-col :span="12">
                            <div
                                :class="['flex', 'text-left font-size-df', isMultiLine(displayData[activeIndex]?.stopStartTime) ? 'align-start' : 'align-center']">
                                <div class="color-blue flex space-between align-center">
                                    <div class="common-dian margin-right-sm"></div>
                                    <div class="d-c-title">停水开始时间:</div>
                                </div>
                                <div class="margin-left-sm text-justify">
                                    {{ displayData[activeIndex]?.stopStartTime }}
                                </div>
                            </div>
                        </el-col>

                        <!-- 停水结束时间 -->
                        <el-col :span="12">
                            <div
                                :class="['flex', 'text-left font-size-df', isMultiLine(displayData[activeIndex]?.stopEndTime) ? 'align-start' : 'align-center']">
                                <div class="color-blue flex space-between align-center">
                                    <div class="common-dian margin-right-sm"></div>
                                    <div class="d-c-title">停水结束时间:</div>
                                </div>
                                <div class="margin-left-sm text-justify">
                                    {{ displayData[activeIndex]?.stopEndTime }}
                                </div>
                            </div>
                        </el-col>

                        <!-- 停水范围（多行文本） -->
                        <el-col :span="24">
                            <div
                                :class="['flex', 'text-left font-size-df', isMultiLine(displayData[activeIndex]?.stopRange) ? 'align-start' : 'align-center']">
                                <div class="color-blue flex space-between align-center">
                                    <div class="common-dian margin-right-sm"></div>
                                    <div class="d-c-title">停水范围:</div>
                                </div>
                                <div class="margin-left-sm text-justify">
                                    {{ displayData[activeIndex]?.stopRange }}
                                </div>
                            </div>
                        </el-col>

                        <!-- 停水原因（多行文本） -->
                        <el-col :span="24">
                            <div
                                :class="['flex', 'text-left font-size-df', isMultiLine(displayData[activeIndex]?.stopReason) ? 'align-start' : 'align-center']">
                                <div class="color-blue flex space-between align-center">
                                    <div class="common-dian margin-right-sm"></div>
                                    <div class="d-c-title">原&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;因:</div>
                                </div>
                                <div class="margin-left-sm text-justify">
                                    {{ displayData[activeIndex]?.stopReason }}
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { get } from '../utils/request'; // HTTP请求工具

// 关闭按钮的三种状态图片
import normalImg from '../assets/img/close-btn.png'
import hoverImg from '../assets/img/close-hover.png'
import clickImg from '../assets/img/close-click.png'

export default {
    name: 'Announcement',
    setup() {
        // -------------------------- 响应式变量 --------------------------
        let intervalId; // 定时刷新数据的计时器ID
        const scrollContainer = ref(null); // 滚动容器DOM引用
        const scrollWrapper = ref(null); // 滚动内容DOM引用
        const activeIndex = ref(null); // 当前激活的公告索引（用于详情弹窗）
        const isDetailVisible = ref(false); // 详情弹窗是否可见
        const originalData = ref([]); // 存储原始公告数据（接口返回）
        const displayData = ref([]);  // 用于显示的数据（原始数据 + 克隆项，实现无缝滚动）
        const currentIndex = ref(0); // 当前滚动到的索引
        const timer = ref(null); // 滚动计时器ID
        const hoverState = reactive([]); // 记录列表项的hover状态
        const btnHoverState = reactive([]); // 记录按钮的hover状态
        const btnClickedState = reactive([]); // 记录按钮的点击状态
        const isScrolling = ref(true); // 是否正在自动滚动
        const scrollHeight = ref(0); // 滚动内容总高度
        const itemHeight = ref(0); // 单个列表项的高度
        const containerHeight = ref(0); // 滚动容器的高度
        const visibleItemCount = ref(3); // 同时显示的列表项数量
        const isTransitioning = ref(false); // 是否正在滚动过渡动画中
        // -------------------------- 初始化与状态管理 --------------------------
        /**
         * 初始化状态数组（hover、按钮状态等）
         * 为每个列表项初始化状态，避免undefined导致的样式问题
         */
        const initStates = () => {
            hoverState.length = 0;
            btnHoverState.length = 0;
            btnClickedState.length = 0;

            displayData.value.forEach((_, index) => {
                hoverState[index] = false;
                btnHoverState[index] = false;
                btnClickedState[index] = false;
            });
        };

        // -------------------------- 计算属性（样式相关） --------------------------
        /**
         * 列表项hover时的样式类
         * @returns {Function} 返回根据索引判断的样式类
         */
        const hoverClass = computed(() => {
            return (index) => hoverState[index] ? 'hoverAn' : '';
        });

        /**
         * 按钮hover时的样式类
         * @returns {Function} 返回根据索引判断的样式类
         */
        const hoverBtnClass = computed(() => {
            return (index) => btnHoverState[index] ? 'hoverBtn color-white' : '';
        });

        /**
         * 按钮点击时的样式类
         * @returns {Function} 返回根据索引判断的样式类
         */
        const clickBtnClass = computed(() => {
            return (index) => btnClickedState[index] ? 'clickBtn color-white' : '';
        });

        /**
         * 列表项内容的颜色类（hover时变色）
         * @returns {Function} 返回根据索引判断的样式类
         */
        const colorClass = computed(() => {
            return (index) => hoverState[index] ? 'color-E3AE3B' : 'color-D5';
        });

        /**
         * 列表项标题的颜色类（hover时变色）
         * @returns {Function} 返回根据索引判断的样式类
         */
        const hoverTitle = computed(() => {
            return (index) => hoverState[index] ? 'color-white' : 'color-D5';
        });

        // -------------------------- 事件处理（交互逻辑） --------------------------
        /**
         * 列表项鼠标悬停事件
         * @param {Number} index - 列表项索引
         * 功能：1. 标记hover状态 2. 同步按钮hover状态 3. 停止自动滚动
         */
        const handleHover = (index) => {
            hoverState[index] = true;
            btnHoverState[index] = true; // 同步按钮hover状态
            stopScrolling(); // 悬停时停止滚动
        };

        /**
         * 列表项鼠标离开事件
         * @param {Number} index - 列表项索引
         * 功能：1. 清除hover状态 2. 同步清除按钮hover状态 3. 恢复自动滚动
         */
        const handleMouseLeave = (index) => {
            hoverState[index] = false;
            // 只有按钮未被点击时才清除hover状态
            if (!btnClickedState[index]) {
                btnHoverState[index] = false;
            }
            resumeScrolling(); // 离开时恢复滚动
        };

        /**
         * 按钮鼠标悬停事件
         * @param {Number} index - 按钮索引
         */
        const handleBtnHover = (index) => {
            btnHoverState[index] = true;
        };

        /**
         * 按钮鼠标离开事件
         * @param {Number} index - 按钮索引
         * 功能：仅在列表项也离开hover状态时才清除按钮hover
         */
        const handleMouseBtnLeave = (index) => {
            if (!hoverState[index] && !btnClickedState[index]) {
                btnHoverState[index] = false;
            }
        };

        /**
         * 点击“查看详情”按钮
         * @param {Number} index - 按钮索引
         * 功能：1. 显示详情弹窗 2. 记录当前激活的公告索引 3. 标记按钮点击状态
         */
        const handleBtnClick = (index) => {
            // 处理克隆项的索引（克隆项是originalData的重复，需转换为原始索引）
            const realIndex = index >= originalData.value.length ?
                index - originalData.value.length : index;

            isDetailVisible.value = true; // 显示弹窗
            stopScrolling(); // 停止滚动
            activeIndex.value = realIndex; // 记录激活项

            // 重置所有按钮状态，只激活当前点击的按钮
            btnClickedState.forEach((_, i) => {
                btnClickedState[i] = false;
            });
            btnClickedState[index] = true;
            btnHoverState[index] = false; // 清除hover状态，显示点击状态
        };

        // -------------------------- 滚动逻辑（核心功能） --------------------------
        /**
         * 开始自动滚动
         * 功能：只有当数据量大于可见数量时才启动滚动，避免无效滚动
         */
        const startScrolling = () => {
            if (timer.value) clearInterval(timer.value); // 清除现有计时器

            if (originalData.value.length > visibleItemCount.value) {
                isScrolling.value = true;
                // 每5秒滚动一次
                timer.value = setInterval(scrollToNextItem, 5000);
            } else {
                isScrolling.value = false; // 数据不足时停止滚动
            }
        };

        /**
         * 停止自动滚动
         */
        const stopScrolling = () => {
            isScrolling.value = false;
            clearInterval(timer.value);
        };

        /**
         * 恢复自动滚动
         */
        const resumeScrolling = () => {
            if (!isDetailVisible.value) { // 弹窗关闭时才恢复滚动
                isScrolling.value = true;
                startScrolling();
            }
        };

        /**
         * 判断文本是否为多行（用于调整布局）
         * @param {String} text - 要判断的文本
         * @returns {Boolean} 是否为多行
         */
        const isMultiLine = (text) => {
            if (!text) return false;
            // 假设单行最多显示30个字符（可根据实际布局调整）
            return text.length > 30;
        };

        /**
         * 滚动到下一项（核心滚动逻辑）
         * 功能：实现平滑滚动，到达末尾时无缝衔接至开头
         */
        const scrollToNextItem = () => {
            // 正在过渡或未启用滚动时不执行
            if (isTransitioning.value || !isScrolling.value) return;

            isTransitioning.value = true;
            currentIndex.value++; // 累加滚动索引

            // 应用滚动动画（向上移动一个项的高度）
            scrollWrapper.value.style.transition = 'transform 0.5s ease-in-out';
            scrollWrapper.value.style.transform = `translateY(-${currentIndex.value * itemHeight.value}px)`;

            // 当滚动到克隆项区域时，无缝重置到起始位置
            if (currentIndex.value >= originalData.value.length) {
                setTimeout(() => {
                    // 取消动画，瞬间重置位置（用户无感知）
                    scrollWrapper.value.style.transition = 'none';
                    scrollWrapper.value.style.transform = 'translateY(0)';
                    currentIndex.value = 0; // 重置索引
                    isTransitioning.value = false;
                }, 500); // 等待当前动画结束（0.5秒）
            } else {
                // 正常滚动结束，重置过渡状态
                setTimeout(() => {
                    isTransitioning.value = false;
                }, 500);
            }
        };

        /**
         * 计算滚动相关的尺寸
         * 功能：1. 计算单个列表项的高度 2. 设置容器高度为可见项总高度
         */
        const calculateDimensions = () => {
            if (scrollWrapper.value && scrollWrapper.value.children.length > 0) {
                // 计算单个项的高度（包含margin-top）
                itemHeight.value = scrollWrapper.value.children[0].offsetHeight + 10;
                // 容器高度 = 可见项数量 * 单个项高度
                containerHeight.value = itemHeight.value * visibleItemCount.value;

                if (scrollContainer.value) {
                    scrollContainer.value.style.height = `${containerHeight.value}px`;
                }
            }
        };

        /**
         * 准备用于无缝滚动的数据
         * 功能：1. 复制原始数据 2. 在末尾添加克隆项（原始数据的前3项）
         * 目的：当滚动到末尾时，克隆项与原始数据的开头衔接，实现“无缝”效果
         */
        const prepareSeamlessData = () => {
            if (!originalData.value.length) {
                // Mock数据 - 用于测试和开发
                displayData.value = [
                    {
                        id: 1,
                        stopTypeName: '紧急停水',
                        stopReason: '主管道破裂抢修',
                        stopAddress: '和平区三好街1-100号',
                        stopStartTime: '2024-01-15 08:00',
                        stopEndTime: '2024-01-15 18:00',
                        publishUnit: '沈阳市自来水集团有限公司',
                        valveInfo: '主干管阀门V001-V005',
                        stopRange: '和平区三好街全段、文化路1-50号、南京南街100-200号等区域，预计影响用户约5000户'
                    },
                    {
                        id: 2,
                        stopTypeName: '计划停水',
                        stopReason: '供水管网改造工程',
                        stopAddress: '沈河区中街路50-150号',
                        stopStartTime: '2024-01-16 09:00',
                        stopEndTime: '2024-01-16 17:00',
                        publishUnit: '沈阳市自来水集团有限公司',
                        valveInfo: '分支管阀门B201-B210',
                        stopRange: '沈河区中街路全段、大西路1-30号、小西路全段，预计影响用户约3000户'
                    },
                    {
                        id: 3,
                        stopTypeName: '维护停水',
                        stopReason: '水质检测设备维护',
                        stopAddress: '铁西区建设大路200-300号',
                        stopStartTime: '2024-01-17 06:00',
                        stopEndTime: '2024-01-17 12:00',
                        publishUnit: '沈阳市自来水集团有限公司',
                        valveInfo: '检测站阀门T301-T305',
                        stopRange: '铁西区建设大路200-300号段、工人村1-5区、劳动公园周边区域，预计影响用户约2000户'
                    },
                    {
                        id: 4,
                        stopTypeName: '紧急停水',
                        stopReason: '供水泵站设备故障',
                        stopAddress: '皇姑区黄河南大街全段',
                        stopStartTime: '2024-01-18 10:00',
                        stopEndTime: '2024-01-18 20:00',
                        publishUnit: '沈阳市自来水集团有限公司',
                        valveInfo: '泵站主阀门P401-P408',
                        stopRange: '皇姑区黄河南大街全段、长江街1-100号、松花江街全段、嫩江街1-50号等区域，预计影响用户约8000户'
                    },
                    {
                        id: 5,
                        stopTypeName: '计划停水',
                        stopReason: '老旧管网更换',
                        stopAddress: '大东区大北街1-200号',
                        stopStartTime: '2024-01-19 07:00',
                        stopEndTime: '2024-01-19 19:00',
                        publishUnit: '沈阳市自来水集团有限公司',
                        valveInfo: '老城区阀门O501-O515',
                        stopRange: '大东区大北街全段、小北街1-100号、东顺城街全段，预计影响用户约4500户'
                    }
                ];

                // 添加克隆项（数量 = 可见项数量）
                const cloneCount = visibleItemCount.value;
                const originalLength = displayData.value.length;
                for (let i = 0; i < cloneCount; i++) {
                    displayData.value.push(displayData.value[i % originalLength]);
                }

                // 初始化状态数组
                initStates();
                return;
            }

            // 复制原始数据
            displayData.value = [...originalData.value];

            // 添加克隆项（数量 = 可见项数量）
            const cloneCount = visibleItemCount.value;
            for (let i = 0; i < cloneCount; i++) {
                displayData.value.push(originalData.value[i % originalData.value.length]);
            }

            // 初始化状态数组
            initStates();
        };

        // -------------------------- 弹窗相关 --------------------------
        const currentImage = ref(normalImg) // 当前显示的关闭按钮图片
        const normalImage = ref(normalImg) //弹窗关闭按钮默认状态
        const hoverImage = ref(hoverImg) //弹窗关闭按钮悬停状态
        const clickImage = ref(clickImg) //弹窗关闭按钮点击状态
        /** 关闭按钮鼠标悬停 */
        const handleMouseEnter = () => {
            currentImage.value = hoverImg
        }

        /** 关闭按钮鼠标离开 */
        const handleMouseLeave1 = () => {
            currentImage.value = normalImg
        }

        /** 关闭按钮鼠标按下 */
        const handleMouseDown = () => {
            currentImage.value = clickImg
        }

        /** 关闭按钮鼠标抬起 */
        const handleMouseUp = (event) => {
            // 根据鼠标是否仍在按钮上决定显示hover还是正常状态
            const isMouseOver = event.relatedTarget?.closest('.d-t-c') !== null
            currentImage.value = isMouseOver ? hoverImg : normalImg
        }

        /** 关闭详情弹窗 */
        const closeD = () => {
            isDetailVisible.value = false;
            activeIndex.value = null;
            // 重置所有按钮状态
            btnClickedState.forEach((_, i) => {
                btnClickedState[i] = false;
            });
            resumeScrolling(); // 关闭弹窗后恢复滚动
            currentImage.value = normalImg
        }

        // -------------------------- 数据请求 --------------------------
        /**
         * 获取停水公告数据
         * 功能：1. 调用接口获取数据 2. 处理数据用于无缝滚动 3. 初始化滚动
         */
        const getWaterNoticeData = async () => {
            try {
                const response = await get('/cloudcall-flowable-api/shenyang-report/screen/stopWaterNotice');
                originalData.value = response.data || []; // 存储原始数据
                prepareSeamlessData(); // 准备显示数据（添加克隆项）

                // 等待DOM更新后计算尺寸并启动滚动
                nextTick(() => {
                    calculateDimensions();
                    startScrolling();
                });
            } catch (error) {
                console.error('获取停水公告失败', error);
            }
        };

        // -------------------------- 监听与生命周期 --------------------------
        // 监听显示数据变化，重新初始化状态和计算尺寸
        watch(() => displayData.value, () => {
            initStates();
            nextTick(calculateDimensions);
        }, { deep: true });

        // 监听窗口大小变化，重新计算尺寸（适配响应式）
        watch(() => ({
            width: window.innerWidth,
            height: window.innerHeight
        }), calculateDimensions, { deep: true });

        // 组件挂载时初始化
        onMounted(() => {
            getWaterNoticeData(); // 首次加载数据
            intervalId = setInterval(getWaterNoticeData, 60000); // 每分钟刷新一次数据
            window.addEventListener('resize', calculateDimensions); // 监听窗口 resize
        });

        // 组件卸载时清理
        onUnmounted(() => {
            clearInterval(intervalId); // 清除定时刷新
            stopScrolling(); // 停止滚动
            window.removeEventListener('resize', calculateDimensions); // 移除事件监听
        });

        // -------------------------- 暴露给模板的数据和方法 --------------------------
        return {
            getWaterNoticeData,
            scrollContainer,
            scrollWrapper,
            activeIndex,
            isDetailVisible,
            displayData,
            currentIndex,
            hoverState,
            btnHoverState,
            btnClickedState,
            isScrolling,
            hoverClass,
            hoverBtnClass,
            clickBtnClass,
            colorClass,
            hoverTitle,
            handleHover,
            handleMouseLeave,
            handleBtnHover,
            handleMouseBtnLeave,
            handleBtnClick,
            startScrolling,
            stopScrolling,
            resumeScrolling,
            currentImage,
            normalImage,
            hoverImage,
            clickImage,
            handleMouseEnter,
            handleMouseLeave1,
            handleMouseDown,
            handleMouseUp,
            closeD,
            isMultiLine
        };
    }
};
</script>

<style scoped>
/* 停水公告容器样式 */
.announcement-bg {
    background-image: url('../../src/assets/img/huawu.png');
    background-size: 100% 41px;
    margin-top: 40px;
    height: 41px;
}

.announcement-title {
    height: 21px;
    padding-bottom: 10px;
}

/* 滚动区域样式 */
.overflow-hidden {
    overflow: hidden;
    /* 隐藏超出容器的内容，实现滚动效果 */
}

.scroll-wrapper {
    transition: transform 0.5s ease-in-out;
    /* 滚动过渡动画 */
}

/* 公告项样式 */
.an-con {
    background-image: url('../../src/assets/img/an-bg.png');
    background-size: 100% 90px;
    height: 90px;
    position: relative;
    margin-top: 10px;
}

.an-con.hoverAn {
    background-image: url('../../src/assets/img/an-hover-bg.png');
    /* hover时的背景图 */
}

.an-title {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translate(10px, -50%);
    width: 40px;
}

.an-right {
    margin: 0 50px 0 100px;
    width: 100%;
}

/* 按钮样式 */
.common-btn {
    width: 100px;
    height: 32px;
    line-height: 32px;
    text-align: center;
}

.check-more {
    background-image: url('../../src/assets/img/an-btn.png');
    /* 默认按钮背景 */
}

.hoverBtn {
    background-image: url('../../src/assets/img/an-hover-btn.png');
    /* hover按钮背景 */
}

.clickBtn {
    background-image: url('../../src/assets/img/an-click-btn.png');
    /* 点击状态按钮背景 */
    color: white;
}

/* 详情弹窗样式 */
.dialog-bg {
    background-image: url('/src/assets/img/a-d-b.png');
    max-height: 463px;
    width: 827.8px;
    height: 463px;
    margin: 10px 0;
}

.d-t-b {
    width: 172px;
    height: 64px;
}

.dialog-title {
    width: 180px;
}

.dialog-content {
    height: 300px;
    overflow-y: auto;
    /* 内容超出时显示滚动条 */
}

.d-c-title {
    white-space: nowrap;
    /* 标题不换行 */
}

.row-bg>div:not(:last-child) {
    margin-bottom: 20px;
    /* 弹窗内容项之间的间距 */
}
</style>
